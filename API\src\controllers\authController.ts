import { Request, Response, NextFunction } from "express";
import { UserRole, UserStatus } from "@prisma/client";
import { prisma } from "../config/database";
import { AuthUtils } from "../utils/auth";
import { createError } from "../middleware/errorHandler";

interface RegisterRequest {
  email: string;
  password: string;
  full_name: string;
  mobile_number?: string;
  usn: string;
  course_name: string;
  batch_year: number;
  role: UserRole;
  tenant_id: number;
}

interface LoginRequest {
  email: string;
  password: string;
}

interface RefreshTokenRequest {
  refreshToken: string;
}

/**
 * Register a new user
 */
export const register = async (req: Request<{}, {}, RegisterRequest>, res: Response, next: NextFunction) => {
  try {
    const { email, password, full_name, mobile_number, usn, course_name, batch_year, role, tenant_id } = req.body;

    // Verify tenant exists and is active
    const tenant = await prisma.tenant.findFirst({
      where: {
        id: tenant_id,
        is_active: true,
      },
    });

    if (!tenant) {
      throw createError("Invalid or inactive tenant", 400);
    }

    // Check if user already exists within the tenant
    const existingUser = await prisma.user.findFirst({
      where: {
        tenant_id,
        OR: [{ email }, { usn }],
      },
    });

    if (existingUser) {
      if (existingUser.email === email) {
        throw createError("User with this email already exists in this organization", 409);
      }
      if (existingUser.usn === usn) {
        throw createError("User with this USN already exists in this organization", 409);
      }
    }

    // Hash password
    const hashedPassword = await AuthUtils.hashPassword(password);

    // Find or create course
    let course = await prisma.course.findFirst({
      where: {
        tenant_id,
        course_name,
      },
    });

    if (!course) {
      course = await prisma.course.create({
        data: {
          tenant_id,
          course_name,
        },
      });
    }

    // Create user
    const user = await prisma.user.create({
      data: {
        tenant_id,
        email,
        password_hash: hashedPassword,
        full_name,
        mobile_number: mobile_number ?? null,
        usn,
        role,
        account_status: UserStatus.PENDING, // Requires admin approval
      },
      select: {
        id: true,
        tenant_id: true,
        email: true,
        full_name: true,
        usn: true,
        role: true,
        account_status: true,
        created_at: true,
      },
    });

    // Create user profile
    await prisma.userProfile.create({
      data: {
        user_id: user.id,
        tenant_id,
        course_id: course.id,
        batch_year,
        privacy_settings: {
          show_email: false,
          show_mobile: false,
          show_linkedin: true,
        },
      },
    });

    res.status(201).json({
      message: "Registration successful. Your account is pending approval.",
      user: {
        ...user,
        course_name,
        batch_year,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Login user
 */
export const login = async (req: Request<{}, {}, LoginRequest>, res: Response, next: NextFunction) => {
  try {
    const { email, password } = req.body;

    // Find user with tenant information
    const user = await prisma.user.findFirst({
      where: {
        email,
        tenant: {
          is_active: true,
        },
      },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
            is_active: true,
          },
        },
      },
    });

    if (!user) {
      throw createError("Invalid email or password", 401);
    }

    // Check password
    const isPasswordValid = await AuthUtils.comparePassword(password, user.password_hash);
    if (!isPasswordValid) {
      throw createError("Invalid email or password", 401);
    }

    // Check user status
    if (user.account_status === UserStatus.REJECTED) {
      throw createError("Your account has been rejected. Please contact admin.", 403);
    }

    if (user.account_status === UserStatus.DEACTIVATED) {
      throw createError("Your account has been deactivated. Please contact admin.", 403);
    }

    if (user.account_status === UserStatus.PENDING) {
      throw createError("Your account is pending approval. Please wait for admin approval.", 403);
    }

    // Generate tokens
    const tokens = AuthUtils.generateTokenPair({
      id: user.id.toString(),
      email: user.email,
      role: user.role,
      account_status: user.account_status,
      tenant_id: user.tenant_id,
    });

    res.json({
      message: "Login successful",
      accessToken: tokens.accessToken,
      user: {
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        account_status: user.account_status,
        tenant: user.tenant,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Logout user
 */
export const logout = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Clear refresh token cookie
    res.clearCookie("refreshToken");

    res.json({
      message: "Logout successful",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Refresh access token
 */
export const refreshToken = async (req: Request<{}, {}, RefreshTokenRequest>, res: Response, next: NextFunction) => {
  try {
    const { refreshToken } = req.body;

    // Also check cookie if not in body
    const token = refreshToken || req.cookies.refreshToken;

    if (!token) {
      throw createError("Refresh token is required", 401);
    }

    // Verify refresh token
    const payload = AuthUtils.verifyRefreshToken(token);

    // Check if user still exists and is active
    const user = await prisma.user.findUnique({
      where: { id: parseInt(payload.userId) },
      include: {
        tenant: {
          select: {
            is_active: true,
          },
        },
      },
    });

    if (!user || !user.tenant.is_active) {
      throw createError("User not found or tenant inactive", 401);
    }

    if (user.account_status !== UserStatus.APPROVED) {
      throw createError("Account is not approved", 403);
    }

    // Generate new tokens
    const tokens = AuthUtils.generateTokenPair({
      id: user.id.toString(),
      email: user.email,
      role: user.role,
      account_status: user.account_status,
      tenant_id: user.tenant_id,
    });

    // Set new refresh token as httpOnly cookie
    res.cookie("refreshToken", tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    });

    res.json({
      message: "Token refreshed successfully",
      accessToken: tokens.accessToken,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get current user
 */
export const getCurrentUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const user = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
          },
        },
        profile: {
          include: {
            course: {
              select: {
                course_name: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    res.json({
      user,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Verify email (placeholder for future implementation)
 */
export const verifyEmail = async (req: Request, res: Response, next: NextFunction) => {
  try {
    res.status(501).json({
      message: "Email verification feature coming soon",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Forgot password (placeholder for future implementation)
 */
export const forgotPassword = async (req: Request, res: Response, next: NextFunction) => {
  try {
    res.status(501).json({
      message: "Forgot password feature coming soon",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Reset password (placeholder for future implementation)
 */
export const resetPassword = async (req: Request, res: Response, next: NextFunction) => {
  try {
    res.status(501).json({
      message: "Reset password feature coming soon",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};
