import { body, param, query, validationResult } from "express-validator";
import { Request, Response, NextFunction } from "express";
import { UserRole } from "@prisma/client";
import { AuthUtils } from "../utils/auth";
/**
 * Middleware to handle validation errors
 */
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map((error) => ({
      field: error.type === "field" ? error.path : "unknown",
      message: error.msg,
      value: error.type === "field" ? error.value : undefined,
    }));

    res.status(400).json({
      error: "Validation failed",
      details: errorMessages,
      timestamp: new Date().toISOString(),
    });
    return;
  }
  next();
};

// Auth validation schemas
export const registerValidation = [
  body("email").isEmail().normalizeEmail().withMessage("Please provide a valid email address"),

  body("password")
    .isLength({ min: 8 })
    .withMessage("Password must be at least 8 characters long")
    .custom((password) => {
      const validation = AuthUtils.validatePassword(password);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(", "));
      }
      return true;
    }),

  body("name").trim().isLength({ min: 2, max: 100 }).withMessage("Name must be between 2 and 100 characters"),

  body("mobile").optional().isMobilePhone("any").withMessage("Please provide a valid mobile number"),

  body("usn")
    .trim()
    .isLength({ min: 6, max: 20 })
    .withMessage("USN must be between 6 and 20 characters")
    .custom((usn) => {
      if (!AuthUtils.validateUSN(usn)) {
        throw new Error("Invalid USN format");
      }
      return true;
    }),

  body("course").trim().isLength({ min: 2, max: 100 }).withMessage("Course must be between 2 and 100 characters"),

  body("batch").trim().isLength({ min: 4, max: 4 }).isNumeric().withMessage("Batch must be a 4-digit year"),

  body("role").isIn([UserRole.STUDENT, UserRole.ALUMNUS]).withMessage("Role must be either STUDENT or ALUMNUS"),

  handleValidationErrors,
];

export const loginValidation = [
  body("email").isEmail().normalizeEmail().withMessage("Please provide a valid email address"),

  body("password").notEmpty().withMessage("Password is required"),

  handleValidationErrors,
];

export const refreshTokenValidation = [
  body("refreshToken").notEmpty().withMessage("Refresh token is required"),

  handleValidationErrors,
];

// Profile validation schemas
export const updateProfileValidation = [
  body("name")
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Name must be between 2 and 100 characters"),

  body("bio").optional().trim().isLength({ max: 500 }).withMessage("Bio must not exceed 500 characters"),

  body("mobile").optional().isMobilePhone("any").withMessage("Please provide a valid mobile number"),

  body("linkedinUrl").optional().isURL().withMessage("Please provide a valid LinkedIn URL"),

  body("githubUrl").optional().isURL().withMessage("Please provide a valid GitHub URL"),

  body("portfolioUrl").optional().isURL().withMessage("Please provide a valid portfolio URL"),

  body("company").optional().trim().isLength({ max: 100 }).withMessage("Company name must not exceed 100 characters"),

  body("jobTitle").optional().trim().isLength({ max: 100 }).withMessage("Job title must not exceed 100 characters"),

  body("experience").optional().isInt({ min: 0, max: 50 }).withMessage("Experience must be between 0 and 50 years"),

  body("location").optional().trim().isLength({ max: 100 }).withMessage("Location must not exceed 100 characters"),

  handleValidationErrors,
];

// Common validation schemas
export const idValidation = [param("id").isLength({ min: 1 }).withMessage("ID is required"), handleValidationErrors];

export const paginationValidation = [
  query("page").optional().isInt({ min: 1 }).withMessage("Page must be a positive integer"),

  query("limit").optional().isInt({ min: 1, max: 100 }).withMessage("Limit must be between 1 and 100"),

  handleValidationErrors,
];

// Connection validation schemas
export const connectionRequestValidation = [
  body("receiverId").notEmpty().withMessage("Receiver ID is required"),

  body("message").optional().trim().isLength({ max: 500 }).withMessage("Message must not exceed 500 characters"),

  handleValidationErrors,
];

export const connectionResponseValidation = [
  body("status").isIn(["ACCEPTED", "REJECTED", "BLOCKED"]).withMessage("Status must be ACCEPTED, REJECTED, or BLOCKED"),

  handleValidationErrors,
];

// Post validation schemas
export const createPostValidation = [
  body("title").trim().isLength({ min: 5, max: 200 }).withMessage("Title must be between 5 and 200 characters"),

  body("content").trim().isLength({ min: 10, max: 5000 }).withMessage("Content must be between 10 and 5000 characters"),

  body("type").isIn(["ADVICE", "GENERAL", "ANNOUNCEMENT"]).withMessage("Type must be ADVICE, GENERAL, or ANNOUNCEMENT"),

  body("isPublic").isBoolean().withMessage("isPublic must be a boolean"),

  body("imageUrl").optional().isURL().withMessage("Please provide a valid image URL"),

  handleValidationErrors,
];

// Message validation schemas
export const sendMessageValidation = [
  body("receiverId").notEmpty().withMessage("Receiver ID is required"),

  body("content")
    .trim()
    .isLength({ min: 1, max: 2000 })
    .withMessage("Message content must be between 1 and 2000 characters"),

  handleValidationErrors,
];

// Notification preferences validation
export const notificationPreferencesValidation = [
  body("emailMessageReceived").optional().isBoolean().withMessage("emailMessageReceived must be a boolean"),
  body("emailConnectionRequest").optional().isBoolean().withMessage("emailConnectionRequest must be a boolean"),
  body("emailPostCreated").optional().isBoolean().withMessage("emailPostCreated must be a boolean"),
  body("emailSystemUpdates").optional().isBoolean().withMessage("emailSystemUpdates must be a boolean"),
  body("inAppMessageReceived").optional().isBoolean().withMessage("inAppMessageReceived must be a boolean"),
  body("inAppConnectionRequest").optional().isBoolean().withMessage("inAppConnectionRequest must be a boolean"),
  body("inAppPostCreated").optional().isBoolean().withMessage("inAppPostCreated must be a boolean"),
  body("inAppSystemUpdates").optional().isBoolean().withMessage("inAppSystemUpdates must be a boolean"),
  body("emailDigest").optional().isBoolean().withMessage("emailDigest must be a boolean"),
  body("emailDigestFrequency")
    .optional()
    .isIn(["DAILY", "WEEKLY", "MONTHLY"])
    .withMessage("emailDigestFrequency must be DAILY, WEEKLY, or MONTHLY"),

  handleValidationErrors,
];
