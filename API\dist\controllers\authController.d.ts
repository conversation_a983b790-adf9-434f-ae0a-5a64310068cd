import { Request, Response, NextFunction } from "express";
import { UserRole } from "@prisma/client";
interface RegisterRequest {
    email: string;
    password: string;
    full_name: string;
    mobile_number?: string;
    usn: string;
    course_name: string;
    batch_year: number;
    role: UserRole;
    tenant_id: number;
}
interface LoginRequest {
    email: string;
    password: string;
}
interface RefreshTokenRequest {
    refreshToken: string;
}
export declare const register: (req: Request<{}, {}, RegisterRequest>, res: Response, next: NextFunction) => Promise<void>;
export declare const login: (req: Request<{}, {}, LoginRequest>, res: Response, next: NextFunction) => Promise<void>;
export declare const logout: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const refreshToken: (req: Request<{}, {}, RefreshTokenRequest>, res: Response, next: NextFunction) => Promise<void>;
export declare const getCurrentUser: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const verifyEmail: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const forgotPassword: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const resetPassword: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export {};
//# sourceMappingURL=authController.d.ts.map