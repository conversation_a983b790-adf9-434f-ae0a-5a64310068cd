"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const express_1 = __importDefault(require("express"));
const helmet_1 = __importDefault(require("helmet"));
const http_1 = require("http");
const morgan_1 = __importDefault(require("morgan"));
dotenv_1.default.config();
const cors_2 = require("./config/cors");
const errorHandler_1 = require("./middleware/errorHandler");
const notFoundHandler_1 = require("./middleware/notFoundHandler");
const rateLimiter_1 = require("./middleware/rateLimiter");
const loggerService_1 = require("./services/loggerService");
const auth_1 = __importDefault(require("./routes/auth"));
const user_1 = __importDefault(require("./routes/user"));
const app = (0, express_1.default)();
const PORT = parseInt(process.env.PORT || "3001");
const initializeServices = async () => {
    try {
        loggerService_1.Logger.info("Basic services initialized successfully");
    }
    catch (error) {
        loggerService_1.Logger.error("Failed to initialize services:", error);
        process.exit(1);
    }
};
const server = (0, http_1.createServer)(app);
server.keepAliveTimeout = parseInt(process.env.KEEP_ALIVE_TIMEOUT || "65000");
server.headersTimeout = parseInt(process.env.HEADERS_TIMEOUT || "66000");
app.use((0, helmet_1.default)({
    crossOriginResourcePolicy: { policy: "cross-origin" },
    contentSecurityPolicy: process.env.NODE_ENV === "production",
}));
app.use((0, cors_1.default)(cors_2.corsOptions));
app.use(express_1.default.json({
    limit: "10mb",
    strict: true,
    type: ["application/json", "application/*+json"],
}));
app.use(express_1.default.urlencoded({
    extended: true,
    limit: "10mb",
    parameterLimit: 1000,
}));
app.use((0, cookie_parser_1.default)());
if (process.env.NODE_ENV === "development") {
    app.use((0, morgan_1.default)("dev"));
}
else {
    app.use((0, morgan_1.default)("combined"));
}
app.use(rateLimiter_1.rateLimiter);
app.get("/health", (_req, res) => {
    res.status(200).json({
        status: "OK",
        message: "Alumni Portal API is running",
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        uptime: process.uptime(),
    });
});
app.get("/ready", (_req, res) => {
    res.status(200).json({
        status: "ready",
        timestamp: new Date().toISOString(),
    });
});
app.get("/live", (_req, res) => {
    res.status(200).json({
        status: "alive",
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
    });
});
app.use("/api/auth", auth_1.default);
app.use("/api/users", user_1.default);
app.use(notFoundHandler_1.notFoundHandler);
app.use(errorHandler_1.errorHandler);
const startServer = async () => {
    try {
        await initializeServices();
        server.listen(PORT, () => {
            console.log(`🚀 Alumni Portal API server running on port http://localhost:${PORT}`);
            console.log(`📊 Environment: ${process.env.NODE_ENV}`);
            console.log(`🔗 Health check: http://localhost:${PORT}/health`);
            console.log(`⚡ Enhanced for 10K+ concurrent users`);
            console.log(`🔄 Redis distributed caching: ${process.env.ENABLE_DISTRIBUTED_CACHE === "true" ? "Enabled" : "Disabled"}`);
            console.log(`📊 Clustering: ${process.env.CLUSTER_ENABLED === "true" ? "Enabled" : "Disabled"}`);
        });
    }
    catch (error) {
        console.error("Failed to start server:", error);
        process.exit(1);
    }
};
startServer();
process.on("SIGTERM", () => {
    console.log("SIGTERM received, shutting down gracefully");
    server.close(() => {
        console.log("HTTP server closed");
        process.exit(0);
    });
});
process.on("SIGINT", () => {
    console.log("SIGINT received, shutting down gracefully");
    server.close(() => {
        console.log("HTTP server closed");
        process.exit(0);
    });
});
exports.default = app;
//# sourceMappingURL=app.js.map