const axios = require("axios");

const API_BASE_URL = "http://localhost:5000/api";

async function testAdminRegistration() {
  try {
    console.log("🧪 Testing Admin Registration (without USN)...");

    // Test 1: Register a tenant admin without USN
    const adminData = {
      email: `admin${Date.now()}@testuni.edu`, // Unique email to avoid conflicts
      password: "AdminPass123!",
      full_name: "<PERSON> Admin",
      mobile_number: "+************", // Valid Indian mobile number
      role: "TENANT_ADMIN",
      tenant_id: 1,
      // Note: No USN, course_name, or batch_year provided
    };

    const adminResponse = await axios.post(`${API_BASE_URL}/auth/register`, adminData);
    console.log("✅ Admin registration successful:", {
      message: adminResponse.data.message,
      user: adminResponse.data.user,
      status: adminResponse.status,
    });

    // Test 2: Register a student with USN (should still require USN)
    const studentData = {
      email: "<EMAIL>",
      password: "StudentPass123!",
      full_name: "<PERSON>",
      mobile_number: "+************", // Valid Indian mobile number
      usn: "CS2024001",
      course_name: "Computer Science",
      batch_year: 2024,
      role: "STUDENT",
      tenant_id: 1,
    };

    const studentResponse = await axios.post(`${API_BASE_URL}/auth/register`, studentData);
    console.log("✅ Student registration successful:", {
      message: studentResponse.data.message,
      user: studentResponse.data.user,
      status: studentResponse.status,
    });

    // Test 3: Try to register a student without USN (should fail)
    const invalidStudentData = {
      email: "<EMAIL>",
      password: "StudentPass123!",
      full_name: "Bob Student",
      role: "STUDENT",
      tenant_id: 1,
      // Missing USN, course_name, batch_year
    };

    try {
      await axios.post(`${API_BASE_URL}/auth/register`, invalidStudentData);
      console.log("❌ This should have failed!");
    } catch (error) {
      console.log("✅ Student registration correctly failed without USN:", {
        status: error.response.status,
        message: error.response.data.message || error.response.data.errors,
      });
    }

    console.log("🎉 All admin registration tests passed!");
  } catch (error) {
    console.error("❌ Test failed:", {
      status: error.response?.status,
      message: error.response?.data?.message || error.message,
      errors: error.response?.data?.errors,
      details: error.response?.data?.details,
      fullError: error.code || error.message,
    });
    if (error.response?.data?.details) {
      console.log("Validation details:", error.response.data.details);
    }
  }
}

// Run the test
testAdminRegistration();
